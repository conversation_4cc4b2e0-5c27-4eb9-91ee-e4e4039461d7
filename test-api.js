const axios = require('axios');

async function testAPI() {
  try {
    console.log('Testing API connection...');
    
    // Test products endpoint
    const response = await axios.get('http://localhost:9000/api/products');
    console.log('✅ Products API working');
    console.log('Products found:', response.data.length || 'No products');
    
    // Test admin login
    const loginResponse = await axios.post('http://localhost:9000/api/auth/login', {
      username: '<EMAIL>',
      password: 'admin123456'
    });
    console.log('✅ Admin login working');
    console.log('Admin:', loginResponse.data.admin.username);
    
  } catch (error) {
    console.error('❌ API Error:', error.message);
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', error.response.data);
    }
  }
}

testAPI();
